"use client";

import { useEffect, useState } from 'react';

const ProcessCard = ({ processSteps }) => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (typeof window === 'undefined') return;

      // Find process section
      const processSection = document.querySelector('[data-section="process"]');
      if (!processSection) return;

      const rect = processSection.getBoundingClientRect();

      // Calculate scroll progress through the section
      const sectionTop = rect.top;
      const sectionHeight = rect.height;

      // Start moving when section reaches top of viewport
      if (sectionTop <= 0 && sectionTop > -sectionHeight) {
        const progress = Math.abs(sectionTop) / sectionHeight;
        setScrollProgress(Math.min(1, progress));
      } else if (sectionTop > 0) {
        setScrollProgress(0);
      } else {
        setScrollProgress(1);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="w-full h-full relative flex items-center justify-center overflow-hidden">
      {/* Horizontal carousel of cards */}
      <div
        className="flex items-center gap-8"
        style={{
          // Move cards from right to left based on scroll
          transform: `translateX(${100 - (scrollProgress * 150)}vw)`,
          transition: 'none' // No artificial transitions, just natural scroll movement
        }}
      >
        {processSteps.map((step) => (
          <div
            key={step.id}
            className="flex-shrink-0 bg-primary rounded-2xl border border-secondary/20 shadow-lg p-6"
            style={{
              width: '450px',
              height: '280px',
              boxShadow: `0px 4px 20px rgba(0, 0, 0, 0.3)`
            }}
          >
            {/* Horizontal Layout: Number on left, Text on right */}
            <div className="flex items-start gap-6 h-full">
              {/* Step Number - Left Side */}
              <div className="flex-shrink-0 w-16 h-16 bg-accent rounded-full flex items-center justify-center">
                <span className="text-primary text-2xl font-bold font-heading">
                  {step.number}
                </span>
              </div>

              {/* Text Content - Right Side */}
              <div className="flex-1 flex flex-col justify-center">
                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl mb-3">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProcessCard;
